using Microsoft.AspNetCore.Mvc;
using DatVeXe.Models;
using DatVeXe.Services;
using System.Text.Json;
using Microsoft.EntityFrameworkCore;

namespace DatVeXe.Controllers
{
    [Route("[controller]")]
    public class CheckoutController : Controller
    {
        private readonly ILogger<CheckoutController> _logger;
        private readonly IMoMoHelper _moMoHelper;
        private readonly IPaymentService _paymentService;
        private readonly DatVeXeContext _context;

        public CheckoutController(ILogger<CheckoutController> logger, IMoMoHelper moMoHelper, IPaymentService paymentService, DatVeXeContext context)
        {
            _logger = logger;
            _moMoHelper = moMoHelper;
            _paymentService = paymentService;
            _context = context;
        }

        [HttpGet("PaymentCallBack")]
        public async Task<IActionResult> PaymentCallBack()
        {
            try
            {
                _logger.LogInformation("MoMo PaymentCallBack received");

                // Lấy parameters từ query string
                var parameters = Request.Query.ToDictionary(x => x.Key, x => x.Value.ToString());
                _logger.LogInformation($"PaymentCallBack parameters: {string.Join(", ", parameters.Select(p => $"{p.Key}={p.Value}"))}");

                var orderId = parameters.GetValueOrDefault("orderId");
                var resultCode = parameters.GetValueOrDefault("resultCode");

                _logger.LogInformation($"PaymentCallBack - OrderId: {orderId}, ResultCode: {resultCode}");

                if (string.IsNullOrEmpty(orderId))
                {
                    _logger.LogWarning("Missing orderId in PaymentCallBack");
                    return RedirectToAction("PaymentFailure", "Booking", new {
                        errorMessage = "Thiếu thông tin đơn hàng",
                        errorCode = "MISSING_ORDER_ID"
                    });
                }

                // Kiểm tra nếu người dùng chọn "Quay về" hoặc hủy thanh toán
                if (resultCode == "1006" || resultCode == "1003")
                {
                    var originalOrderId = orderId;
                    if (orderId.Contains("_"))
                    {
                        originalOrderId = orderId.Split('_')[0];
                    }

                    _logger.LogInformation($"User cancelled payment for order: {originalOrderId} (ResultCode: {resultCode})");
                    return RedirectToAction("PaymentFailure", "Booking", new {
                        sessionId = originalOrderId,
                        errorMessage = "Thanh toán bị hủy - Bạn đã chọn quay về hoặc hủy thanh toán",
                        errorCode = "USER_CANCELLED",
                        transactionId = originalOrderId
                    });
                }

                // Extract original orderId from uniqueOrderId format: {orderId}_{ticks}
                var originalOrderId = orderId;
                if (orderId.Contains("_"))
                {
                    originalOrderId = orderId.Split('_')[0];
                    _logger.LogInformation($"Extracted original orderId: {originalOrderId} from uniqueOrderId: {orderId}");
                }

                // Xử lý callback thông qua PaymentService
                var paymentResult = await _paymentService.ProcessPaymentCallbackAsync(originalOrderId, parameters);

                if (paymentResult.Success)
                {
                    _logger.LogInformation($"Payment successful for order: {originalOrderId}");
                    return RedirectToAction("PaymentSuccess", "Booking", new {
                        sessionId = originalOrderId,
                        transactionId = paymentResult.TransactionId
                    });
                }
                else
                {
                    _logger.LogWarning($"Payment failed for order: {originalOrderId}, Error: {paymentResult.Message}");
                    return RedirectToAction("PaymentFailure", "Booking", new {
                        sessionId = originalOrderId,
                        errorMessage = paymentResult.Message,
                        errorCode = paymentResult.ErrorCode,
                        transactionId = paymentResult.TransactionId
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing MoMo PaymentCallBack");
                return RedirectToAction("PaymentFailure", "Booking", new {
                    errorMessage = "Có lỗi hệ thống khi xử lý thanh toán",
                    errorCode = "SYSTEM_ERROR"
                });
            }
        }

        // DEPRECATED: Endpoint này không còn được sử dụng
        // MoMo giờ redirect về PaymentCallBack cho tất cả các trường hợp
        [HttpGet("MoMoReturn")]
        public async Task<IActionResult> MoMoReturn()
        {
            try
            {
                _logger.LogInformation("MoMo Return received - User clicked 'Quay về' button");

                // Lấy parameters từ query string
                var parameters = Request.Query.ToDictionary(x => x.Key, x => x.Value.ToString());
                _logger.LogInformation($"MoMoReturn parameters: {string.Join(", ", parameters.Select(p => $"{p.Key}={p.Value}"))}");

                var orderId = parameters.GetValueOrDefault("orderId");
                var resultCode = parameters.GetValueOrDefault("resultCode");

                _logger.LogInformation($"MoMoReturn - OrderId: {orderId}, ResultCode: {resultCode}");

                if (string.IsNullOrEmpty(orderId))
                {
                    _logger.LogWarning("Missing orderId in MoMoReturn");
                    return RedirectToAction("PaymentFailure", "Booking", new {
                        errorMessage = "Thiếu thông tin đơn hàng",
                        errorCode = "MISSING_ORDER_ID"
                    });
                }

                // Extract original orderId from uniqueOrderId format: {orderId}_{ticks}
                var originalOrderId = orderId;
                if (orderId.Contains("_"))
                {
                    originalOrderId = orderId.Split('_')[0];
                    _logger.LogInformation($"Extracted original orderId: {originalOrderId} from uniqueOrderId: {orderId}");
                }

                // Khi người dùng chọn "Quay về" từ MoMo gateway, coi đó là thanh toán thất bại
                // vì người dùng có thể chưa hoàn tất thanh toán
                _logger.LogInformation($"Processing MoMo return as failed payment for order: {originalOrderId} (User chose to return from MoMo gateway without completing payment)");

                return RedirectToAction("PaymentFailure", "Booking", new {
                    sessionId = originalOrderId,
                    errorMessage = "Thanh toán bị hủy - Bạn đã chọn quay về từ cổng thanh toán MoMo",
                    errorCode = "USER_CANCELLED",
                    transactionId = originalOrderId
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing MoMo Return");
                return RedirectToAction("PaymentFailure", "Booking", new {
                    errorMessage = "Có lỗi hệ thống khi xử lý thanh toán",
                    errorCode = "SYSTEM_ERROR"
                });
            }
        }

        [HttpPost("MomoNotify")]
        public async Task<IActionResult> MomoNotify([FromBody] MoMoIpnRequest ipnRequest)
        {
            try
            {
                _logger.LogInformation($"MoMo IPN received: {JsonSerializer.Serialize(ipnRequest)}");

                // Validate signature
                var rawSignature = $"accessKey={Request.Headers["accessKey"]}" +
                                 $"&amount={ipnRequest.amount}" +
                                 $"&extraData={ipnRequest.extraData}" +
                                 $"&message={ipnRequest.message}" +
                                 $"&orderId={ipnRequest.orderId}" +
                                 $"&orderInfo={ipnRequest.orderInfo}" +
                                 $"&orderType={ipnRequest.orderType}" +
                                 $"&partnerCode={ipnRequest.partnerCode}" +
                                 $"&payType={ipnRequest.payType}" +
                                 $"&requestId={ipnRequest.requestId}" +
                                 $"&responseTime={ipnRequest.responseTime}" +
                                 $"&resultCode={ipnRequest.resultCode}" +
                                 $"&transId={ipnRequest.transId}";

                var isValidSignature = _moMoHelper.ValidateSignature(ipnRequest.signature, rawSignature);

                if (!isValidSignature)
                {
                    _logger.LogWarning($"Invalid signature in MoMo IPN for order: {ipnRequest.orderId}");
                    return BadRequest(new MoMoIpnResponse
                    {
                        partnerCode = ipnRequest.partnerCode,
                        requestId = ipnRequest.requestId,
                        orderId = ipnRequest.orderId,
                        resultCode = 97, // Invalid signature
                        message = "Invalid signature",
                        responseTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                    });
                }

                // Process the payment notification
                var parameters = new Dictionary<string, string>
                {
                    ["partnerCode"] = ipnRequest.partnerCode,
                    ["orderId"] = ipnRequest.orderId,
                    ["requestId"] = ipnRequest.requestId,
                    ["amount"] = ipnRequest.amount.ToString(),
                    ["orderInfo"] = ipnRequest.orderInfo,
                    ["orderType"] = ipnRequest.orderType,
                    ["transId"] = ipnRequest.transId.ToString(),
                    ["resultCode"] = ipnRequest.resultCode.ToString(),
                    ["message"] = ipnRequest.message,
                    ["payType"] = ipnRequest.payType,
                    ["responseTime"] = ipnRequest.responseTime.ToString(),
                    ["extraData"] = ipnRequest.extraData,
                    ["signature"] = ipnRequest.signature
                };

                // Extract original orderId from uniqueOrderId format: {orderId}_{ticks}
                var originalOrderId = ipnRequest.orderId;
                if (ipnRequest.orderId.Contains("_"))
                {
                    originalOrderId = ipnRequest.orderId.Split('_')[0];
                    _logger.LogInformation($"IPN - Extracted original orderId: {originalOrderId} from uniqueOrderId: {ipnRequest.orderId}");
                }

                var paymentResult = await _paymentService.ProcessPaymentCallbackAsync(originalOrderId, parameters);

                // Return response to MoMo
                var response = new MoMoIpnResponse
                {
                    partnerCode = ipnRequest.partnerCode,
                    requestId = ipnRequest.requestId,
                    orderId = ipnRequest.orderId,
                    resultCode = paymentResult.Success ? 0 : 99,
                    message = paymentResult.Success ? "Success" : "Failed",
                    responseTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                };

                _logger.LogInformation($"MoMo IPN response: {JsonSerializer.Serialize(response)}");
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing MoMo IPN");
                return StatusCode(500, new MoMoIpnResponse
                {
                    partnerCode = ipnRequest?.partnerCode ?? "",
                    requestId = ipnRequest?.requestId ?? "",
                    orderId = ipnRequest?.orderId ?? "",
                    resultCode = 99,
                    message = "Internal server error",
                    responseTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                });
            }
        }
    }
}
